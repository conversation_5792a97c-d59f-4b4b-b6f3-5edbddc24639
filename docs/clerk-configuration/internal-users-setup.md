# 🔒 Clerk Internal Users Configuration

## 📋 Обзор

Этот документ содержит инструкции по настройке internal users в Clerk для предоставления доступа к калькулятору без активной подписки.

## 🎯 Назначение

Internal users - это пользователи с особыми правами доступа, которые могут использовать калькулятор без оплаты подписки. Обычно это:
- Разработчики
- Тестировщики  
- Администраторы
- VIP пользователи

## ⚙️ Настройка Custom Session Token в Clerk

### 1. Переход в Clerk Dashboard

Откройте Clerk Dashboard по ссылке:
```
https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/sessions
```

### 2. Настройка Custom Session Token

В разделе **Session management → Sessions** найдите секцию **"Customize session token"** и добавьте следующий JSON:

```json
{
	"iu": "{{user.public_metadata.iu}}"
}
```

### 3. Сохранение настроек

Нажмите **Save** для применения изменений.

## 👤 Добавление Internal User

### 1. Переход к пользователю

В Clerk Dashboard перейдите в раздел **Users** и найдите нужного пользователя.

### 2. Редактирование Public Metadata

В профиле пользователя найдите секцию **Public metadata** и добавьте:

```json
{
  "iu": true
}
```

### 3. Сохранение изменений

Нажмите **Save** для применения изменений.

## 🔍 Проверка работы

После настройки пользователь должен:

1. **Перезайти в приложение** (чтобы получить новый JWT токен с custom claims)
2. **Получить доступ к калькулятору** без подписки
3. **В логах backend'а** должно появиться:
   ```
   Internal user detected for user ID: user_xxx, providing free access
   planId: 'internal_free_plan_jwt'
   ```

## 🚨 Важные замечания

- ⚠️ **Перезайти обязательно**: После изменения metadata пользователь должен перезайти в приложение
- 🔄 **JWT токен обновляется**: Новый токен будет содержать custom claim `"iu": true`
- 📏 **Размер токена**: Токен увеличится примерно на 50+ символов
- 🔒 **Безопасность**: Флаг `iu` проверяется на backend'е, подделать его невозможно

## 🛠️ Техническая информация

### Backend логика

Код в `go-landing-backend/internal/api/checkout.go`:

```go
// Check for internal user flag in custom claims
allClaims, claimsOk := GetCustomClaimsFromRequest(r)
if claimsOk && allClaims != nil {
    // Check for 'iu' (internal user) claim
    if iu, exists := allClaims["iu"]; exists {
        if iuBool, ok := iu.(bool); ok && iuBool {
            log.Printf("Internal user detected for user ID: %s, providing free access", userID)
            // Return fake active subscription for internal users
            return c.JSON(http.StatusOK, map[string]interface{}{
                "status":    "active",
                "planId":    "internal_free_plan_jwt",
                "planName":  "Internal Access Plan (JWT)",
                "isActive":  true,
                "isTrialing": false,
                "expiresAt": time.Now().AddDate(10, 0, 0).Format(time.RFC3339),
            })
        }
    }
}
```

### Frontend обработка

Frontend получает `isActive: true` и предоставляет доступ к калькулятору.

## 📝 История изменений

- **2025-06-23**: Создана документация после успешной настройки internal users
- **2025-06-23**: Исправлена проблема с кешированием подписок
- **2025-06-23**: Включена авторизация через email/пароль

## 🔗 Полезные ссылки

- [Clerk Dashboard - Sessions](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/sessions)
- [Clerk Dashboard - Users](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/users)
- [Clerk Documentation - Custom Claims](https://clerk.com/docs/backend-requests/making/custom-session-token)

---

**⚡ Быстрая справка:**
1. Добавить в Clerk Sessions: `{"iu": "{{user.public_metadata.iu}}"}`
2. Добавить в User Metadata: `{"iu": true}`
3. Пользователь должен перезайти в приложение
