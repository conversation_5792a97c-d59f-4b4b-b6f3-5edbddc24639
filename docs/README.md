# 📚 Документация проекта Quer Calculator

## 📁 Структура документации

### 🔒 [Clerk Configuration](./clerk-configuration/)
Документация по настройке Clerk для аутентификации и авторизации:

- **[Internal Users Setup](./clerk-configuration/internal-users-setup.md)** - Настройка internal users для доступа к калькулятору без подписки

## 🚀 Быстрый старт

### Настройка Internal User в Clerk

Если нужно дать пользователю доступ к калькулятору без подписки:

1. **Custom Session Token** в Clerk Dashboard:
   ```json
   {"iu": "{{user.public_metadata.iu}}"}
   ```

2. **Public Metadata** пользователя:
   ```json
   {"iu": true}
   ```

3. **Перезайти в приложение** для получения нового JWT токена

## 🔗 Полезные ссылки

- [Clerk Dashboard - Sessions](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/sessions)
- [Clerk Dashboard - Users](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/users)

---

*Документация создана: 2025-06-23*
