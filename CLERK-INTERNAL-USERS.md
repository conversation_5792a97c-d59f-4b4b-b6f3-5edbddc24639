# 🔒 Быстрая настройка Internal Users в Clerk

## ⚡ Краткая инструкция

### 1. Custom Session Token в Clerk
Добавить в [Clerk Sessions](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/sessions):

```json
{
	"iu": "{{user.public_metadata.iu}}"
}
```

### 2. Public Metadata пользователя
Добавить в профиль пользователя в [Clerk Users](https://dashboard.clerk.com/apps/app_2r32QBMLtFhJnlrPljGv5uchrjQ/instances/ins_2r32QDsEy2OQUCGmat5Mzb11J1X/users):

```json
{
  "iu": true
}
```

### 3. Перезайти в приложение
Пользователь должен перезайти для получения нового JWT токена.

---

📖 **Подробная документация:** [docs/clerk-configuration/internal-users-setup.md](./docs/clerk-configuration/internal-users-setup.md)
