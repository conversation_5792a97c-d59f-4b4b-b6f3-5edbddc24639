{"rustc": 4723136837156968084, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 11832897722978481492, "deps": [[5466618496199522463, "crc32fast", false, 2211359137642126918], [16126238571916867475, "miniz_oxide", false, 4693651273112491731]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-27612f82be30939a/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}