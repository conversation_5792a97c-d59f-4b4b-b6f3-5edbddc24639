import axios from 'axios';
import { getCookie, setCookie } from '../utils/cookies';

// URL для Go API, отвечающего за авторизацию и подписки
// Получаем URL из конфигурации или переменных окружения
const getGoApiUrl = () => {
  // Сначала проверяем переменные окружения React
  if (process.env.REACT_APP_GO_API_URL) {
    return process.env.REACT_APP_GO_API_URL;
  }

  // Проверяем конфигурацию Electron (QUER_CONFIG)
  if (window.QUER_CONFIG && window.QUER_CONFIG.apiURL) {
    return window.QUER_CONFIG.apiURL;
  }

  // Затем проверяем конфигурацию приложения
  if (window.APP_CONFIG && window.APP_CONFIG.apiEndpoints && window.APP_CONFIG.apiEndpoints.go) {
    return window.APP_CONFIG.apiEndpoints.go;
  }

  // По умолчанию используем /api/landing, который будет проксироваться через setupProxy в dev режиме
  // или будет соответствовать настройкам nginx в production
  return '/api/landing';
};

const GO_API_URL = getGoApiUrl();

// Класс для работы с Go API
export class GoAuthApi {
  // Кеширование для предотвращения множественных запросов
  static cachedClerkToken = null;
  static tokenRequestCount = 0;
  static MAX_REQUESTS = 5;

  // Метод для получения токена из cookie с использованием утилит и кешированием
  static async getAuthToken() {
    // Уже аутентифицированы глобально, возвращаем кеш
    if (window.__GLOBAL_AUTH_COMPLETED && this.cachedClerkToken) {
      // Проверяем, не истек ли токен
      if (this.isTokenValid(this.cachedClerkToken)) {
        return this.cachedClerkToken;
      } else {
        console.log('[GoAPI] Cached token expired, getting new one');
        this.cachedClerkToken = null;
        window.__GLOBAL_CLERK_TOKEN_CACHE = null;
      }
    }
    
    // Защита от циклических запросов
    this.tokenRequestCount++;
    if (this.tokenRequestCount > this.MAX_REQUESTS) {
      console.log(`[GoAPI] Token request limit (${this.tokenRequestCount}/${this.MAX_REQUESTS}) reached, using cached token`);
      return this.cachedClerkToken;
    }
    
    // Проверяем глобальный токен-кеш, если установлен
    if (window.__GLOBAL_CLERK_TOKEN_CACHE) {
      // Проверяем валидность глобального кеша
      if (this.isTokenValid(window.__GLOBAL_CLERK_TOKEN_CACHE)) {
        console.log('[GoAPI] Using global token cache');
        this.cachedClerkToken = window.__GLOBAL_CLERK_TOKEN_CACHE;
        return window.__GLOBAL_CLERK_TOKEN_CACHE;
      } else {
        console.log('[GoAPI] Global token cache expired, clearing');
        window.__GLOBAL_CLERK_TOKEN_CACHE = null;
      }
    }
    
    // Проверяем localStorage
    if (localStorage.getItem('__session')) {
      const localStorageToken = localStorage.getItem('__session');
      if (this.isTokenValid(localStorageToken)) {
        console.log('[GoAPI] Using token from localStorage');
        this.cachedClerkToken = localStorageToken;
        window.__GLOBAL_CLERK_TOKEN_CACHE = localStorageToken;
        return localStorageToken;
      } else {
        console.log('[GoAPI] Token in localStorage expired, clearing');
        localStorage.removeItem('__session');
      }
    }
    
    // ИСПРАВЛЕНИЕ: Добавляем проверку токена из Clerk напрямую
    if (window.Clerk && window.Clerk.session) {
      try {
        console.log('[GoAPI] Attempting to get fresh token from Clerk session');
        const clerkToken = await window.Clerk.session.getToken();
        if (clerkToken && this.isTokenValid(clerkToken)) {
          console.log('[GoAPI] Got fresh valid token from Clerk');
          this.cachedClerkToken = clerkToken;
          window.__GLOBAL_CLERK_TOKEN_CACHE = clerkToken;
          localStorage.setItem('__session', clerkToken);
          return clerkToken;
        }
      } catch (clerkError) {
        console.warn('[GoAPI] Error getting token from Clerk session:', clerkError);
      }
    }
    
    if (window.electronAPI) {
      // Устанавливаем глобальный флаг запроса для предотвращения циклов
      if (window.__TOKEN_REQUEST_IN_PROGRESS) {
        console.log('[GoAPI] Token request already in progress, waiting');
        await new Promise(resolve => setTimeout(resolve, 100)); // Небольшая задержка
        return this.cachedClerkToken || window.__GLOBAL_CLERK_TOKEN_CACHE;
      }
      
      try {
        window.__TOKEN_REQUEST_IN_PROGRESS = true;
        
        // В версии 1.5: Приоритизируем получение Clerk Session Token
        if (window.electronAPI.getClerkSessionToken) {
          try {
            const clerkToken = await window.electronAPI.getClerkSessionToken();
            if (clerkToken && this.isTokenValid(clerkToken)) {
              console.log('[GoAPI] Using valid Clerk Session Token from Electron store');
              // Кешируем для предотвращения повторных запросов
              this.cachedClerkToken = clerkToken;
              window.__GLOBAL_CLERK_TOKEN_CACHE = clerkToken;
              window.__TOKEN_REQUEST_IN_PROGRESS = false;
              return clerkToken;
            } else if (clerkToken) {
              console.log('[GoAPI] Clerk token from Electron store expired, getting fresh one');
              
              // Сначала попробуем обновить токен если время истечения близко
              try {
                const payload = JSON.parse(atob(clerkToken.split('.')[1].replace(/-/g, '+').replace(/_/g, '/')));
                const timeToExpiry = payload.exp - Math.floor(Date.now() / 1000);
                
                console.log(`[GoAPI] Time to token expiry: ${timeToExpiry} seconds`);
                
                // Если осталось больше 30 секунд, попробуем использовать токен
                if (timeToExpiry > 30) {
                  console.log('[GoAPI] Token still has significant time left, using it');
                  this.cachedClerkToken = clerkToken;
                  window.__GLOBAL_CLERK_TOKEN_CACHE = clerkToken;
                  window.__TOKEN_REQUEST_IN_PROGRESS = false;
                  return clerkToken;
                }
              } catch (parseError) {
                console.error('[GoAPI] Error parsing token for expiry check:', parseError);
              }
              
              // Отправим запрос на обновление токена через Clerk
              if (window.Clerk && window.Clerk.session) {
                try {
                  // ИСПРАВЛЕНИЕ: Используем правильный метод getToken()
                  const freshToken = await window.Clerk.session.getToken();
                  if (freshToken && this.isTokenValid(freshToken)) {
                    // Сохраняем новый токен
                    await window.electronAPI.storeClerkSessionToken(freshToken);
                    this.cachedClerkToken = freshToken;
                    window.__GLOBAL_CLERK_TOKEN_CACHE = freshToken;
                    window.__TOKEN_REQUEST_IN_PROGRESS = false;
                    console.log('[GoAPI] Got fresh token from Clerk session');
                    return freshToken;
                  }
                } catch (err) {
                  console.error('[GoAPI] Error getting fresh token from Clerk:', err);
                  // Если не получается обновить, поробуем через window.__clerk
                  if (window.__clerk && window.__clerk.session) {
                    try {
                      const freshToken = await window.__clerk.session.getToken();
                      if (freshToken && this.isTokenValid(freshToken)) {
                        await window.electronAPI.storeClerkSessionToken(freshToken);
                        this.cachedClerkToken = freshToken;
                        window.__GLOBAL_CLERK_TOKEN_CACHE = freshToken;
                        window.__TOKEN_REQUEST_IN_PROGRESS = false;
                        console.log('[GoAPI] Got fresh token from __clerk session');
                        return freshToken;
                      }
                    } catch (err2) {
                      console.error('[GoAPI] Error getting token from __clerk:', err2);
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('[GoAPI] Error getting Clerk token:', error);
            // Возвращаем кеш в случае ошибки
            window.__TOKEN_REQUEST_IN_PROGRESS = false;
            if (this.cachedClerkToken) {
              return this.cachedClerkToken;
            }
          }
        }
        
        // Запасной вариант для обратной совместимости
        try {
          const token = await window.electronAPI.getAuthToken();
          if (token && this.isTokenValid(token)) {
            this.cachedClerkToken = token;
            window.__GLOBAL_CLERK_TOKEN_CACHE = token;
          }
          window.__TOKEN_REQUEST_IN_PROGRESS = false;
          return token;
        } catch (error) {
          console.error('[GoAPI] Error getting auth token:', error);
          window.__TOKEN_REQUEST_IN_PROGRESS = false;
          return this.cachedClerkToken;
        }
      } finally {
        // Гарантированно сбрасываем флаг
        setTimeout(() => {
          window.__TOKEN_REQUEST_IN_PROGRESS = false;
        }, 500);
      }
    } else {
      // Получаем токен только из cookie
      const clerkSessionCookie = getCookie('__session');
      if (clerkSessionCookie && this.isTokenValid(clerkSessionCookie)) {
        this.cachedClerkToken = clerkSessionCookie;
        window.__GLOBAL_CLERK_TOKEN_CACHE = clerkSessionCookie;
        return clerkSessionCookie;
      }
      
      const cookieToken = getCookie('auth_token');
      if (cookieToken && this.isTokenValid(cookieToken)) {
        this.cachedClerkToken = cookieToken;
        window.__GLOBAL_CLERK_TOKEN_CACHE = cookieToken;
      }
      return cookieToken || this.cachedClerkToken || null;
    }
  }

  // Метод для проверки валидности JWT токена
  static isTokenValid(token) {
    if (!token) return false;
    
    try {
      // Извлекаем payload из JWT
      const base64Url = token.split('.')[1];
      if (!base64Url) return false;
      
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => 
        '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      ).join(''));
      
      const payload = JSON.parse(jsonPayload);
      
      // Проверяем срок действия токена
      if (payload.exp) {
        const now = Math.floor(Date.now() / 1000);
        const bufferTime = 15; // ОПТИМИЗАЦИЯ: Уменьшаем буфер до 15 секунд (было 60) для менее агрессивной проверки
        
        if (payload.exp <= (now + bufferTime)) {
          console.log(`[GoAPI] Token expires at ${new Date(payload.exp * 1000)}, current time ${new Date()}, buffer: ${bufferTime}s`);
          return false; // Токен истек или истечет в ближайшие 15 секунд
        }
        
        return true;
      }
      
      return false; // Токен без срока действия считаем невалидным
    } catch (error) {
      console.error('[GoAPI] Error checking token validity:', error);
      return false;
    }
  }

  // Метод для принудительного обновления токена
  static async refreshToken() {
    console.log('[GoAPI] Refreshing token...');
    
    // Очищаем кеш
    this.cachedClerkToken = null;
    window.__GLOBAL_CLERK_TOKEN_CACHE = null;
    this.tokenRequestCount = 0;
    
    // ИСПРАВЛЕНИЕ: Также очищаем кеш подписки при обновлении токена
    this.cachedSubscriptionStatus = { isActive: false, isTrialing: false };
    this.lastSubscriptionCheck = 0;
    this.subscriptionChecks = 0;
    console.log('[GoAPI] Subscription cache cleared due to token refresh');
    
    // Если доступен Clerk, пытаемся получить новый токен
    if (window.Clerk && window.Clerk.session) {
      try {
        const freshToken = await window.Clerk.session.getToken();
        if (freshToken) {
          console.log('[GoAPI] Successfully refreshed token from Clerk session');
          
          // Сохраняем новый токен
          if (window.electronAPI && window.electronAPI.storeClerkSessionToken) {
            await window.electronAPI.storeClerkSessionToken(freshToken);
          }
          
          // Обновляем localStorage
          localStorage.setItem('__session', freshToken);
          
          // Обновляем кеш
          this.cachedClerkToken = freshToken;
          window.__GLOBAL_CLERK_TOKEN_CACHE = freshToken;
          
          return freshToken;
        }
      } catch (error) {
        console.error('[GoAPI] Error refreshing token from Clerk session:', error);
        
        // Попробуем через window.__clerk
        if (window.__clerk && window.__clerk.session) {
          try {
            const freshToken = await window.__clerk.session.getToken();
            if (freshToken) {
              console.log('[GoAPI] Successfully refreshed token from __clerk session');
              
              // Сохраняем новый токен
              if (window.electronAPI && window.electronAPI.storeClerkSessionToken) {
                await window.electronAPI.storeClerkSessionToken(freshToken);
              }
              
              // Обновляем localStorage
              localStorage.setItem('__session', freshToken);
              
              // Обновляем кеш
              this.cachedClerkToken = freshToken;
              window.__GLOBAL_CLERK_TOKEN_CACHE = freshToken;
              
              return freshToken;
            }
          } catch (error2) {
            console.error('[GoAPI] Error refreshing token from __clerk session:', error2);
          }
        }
      }
    }
    
    // Пытаемся получить новый токен обычным способом
    return await this.getAuthToken();
  }

  // Метод для сохранения токена с использованием утилит
  static async setAuthToken(token) {
    if (window.electronAPI) {
      // В версии 1.5: Сохраняем Clerk Session Token напрямую
      if (window.electronAPI.storeClerkSessionToken) {
        console.log('[GoAPI] Storing token as Clerk Session Token');
        await window.electronAPI.storeClerkSessionToken(token);
      } else {
        // Запасной вариант для обратной совместимости
        await window.electronAPI.storeAuthToken(token);
      }
    } else {
      // Устанавливаем токен в cookie с безопасными параметрами
      setCookie('auth_token', token, {
        path: '/',
        secure: true,
        httpOnly: true,
        sameSite: 'strict',
        maxAge: 3600 // 1 час
      });
    }
  }

  // Проверка сессии с поддержкой cookie
  static async verifySession(token) {
    try {
      console.log('Verifying session status');
      // В режиме разработки таймаут меньше для быстрой обратной связи
      const timeoutMs = process.env.NODE_ENV === 'development' ? 5000 : 10000;
      
      const headers = {};
      // Добавляем токен в заголовки, только если он указан явно
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await axios.post(`${GO_API_URL}/auth/verify-session`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true, // Важно для чтения и отправки cookie
        timeout: timeoutMs
      });
      console.log('Session verification completed');
      return response.data;
    } catch (error) {
      console.error('Session verification failed');
      // Логируем только необходимые детали без чувствительной информации
      if (error.response) {
        console.error('Error status:', error.response.status);
      } else if (error.request) {
        console.error('No response received from server');
      }
      throw error;
    }
  }

  /* ЗАКОММЕНТИРОВАНО В ВЕРСИИ 1.5: Больше не нужны, так как React-приложение
  использует Clerk Session Token напрямую

  // Получение API токена от Go бэкенда и установка в secure cookie
  static async getToken(sessionToken) {
    try {
      console.log('[GoAuthApi] Requesting authentication token');
      
      // Remove development mode fallback for security
      if (!sessionToken) {
        throw new Error('Authentication required');
      }
      
      // Добавляем отладочную информацию
      console.log('[GoAuthApi] Token length:', sessionToken.length);
      console.log('[GoAuthApi] Token prefix:', sessionToken.substring(0, 10) + '...');
      
      // Проверяем cookies перед запросом
      const cookiesBefore = document.cookie.split(';')
        .map(cookie => cookie.trim())
        .filter(cookie => cookie.length > 0);
      console.log('[GoAuthApi] Cookies before request:', cookiesBefore);
      
      // Для отладки логируем все заголовки запроса
      const headers = {
        'Authorization': `Bearer ${sessionToken}`,
        'Content-Type': 'application/json'
      };
      console.log('[GoAuthApi] Request headers:', headers);
      console.log('[GoAuthApi] Request URL:', `${GO_API_URL}/auth/token`);
      console.log('[GoAuthApi] Request method: POST');
      console.log('[GoAuthApi] withCredentials: true');
      
      const response = await axios.post(`${GO_API_URL}/auth/token`, {}, {
        headers,
        timeout: 8000,
        withCredentials: true // Важно для работы с cookie
      });
      
      // Проверяем cookies после запроса
      const cookiesAfter = document.cookie.split(';')
        .map(cookie => cookie.trim())
        .filter(cookie => cookie.length > 0);
      console.log('[GoAuthApi] Cookies after request:', cookiesAfter);
      
      // Проверяем, добавились ли новые cookies
      const newCookies = cookiesAfter.filter(cookie => !cookiesBefore.includes(cookie));
      console.log('[GoAuthApi] New cookies after request:', newCookies);
      
      console.log('[GoAuthApi] Token request completed with status:', response.status);
      console.log('[GoAuthApi] Response data:', response.data);
      
      return response.data;
    } catch (error) {
      console.error('[GoAuthApi] Token generation failed:', error);
      
      // Логируем только необходимое без чувствительной информации
      if (error.response) {
        console.error('[GoAuthApi] Error status:', error.response.status);
        console.error('[GoAuthApi] Error data:', error.response.data);
        console.error('[GoAuthApi] Error headers:', error.response.headers);
      } else if (error.request) {
        console.error('[GoAuthApi] No response received from server. Request:', error.request);
      } else {
        console.error('[GoAuthApi] Error message:', error.message);
      }
      
      // Для отладки проверяем cookies после ошибки
      const cookiesAfterError = document.cookie.split(';')
        .map(cookie => cookie.trim())
        .filter(cookie => cookie.length > 0);
      console.log('[GoAuthApi] Cookies after error:', cookiesAfterError);
      
      throw error;
    }
  }
  
  // Безопасная установка сессионного cookie через серверный API
  static async setSessionCookie(token) {
    try {
      console.log('[GoAuthApi] Setting up session authentication');
      console.log('[GoAuthApi] Token length:', token.length);
      console.log('[GoAuthApi] Token prefix:', token.substring(0, 10) + '...');
      
      // Проверяем cookies перед запросом
      const cookiesBefore = document.cookie.split(';')
        .map(cookie => cookie.trim())
        .filter(cookie => cookie.length > 0);
      console.log('[GoAuthApi] Cookies before request:', cookiesBefore);
      
      // Логируем заголовки запроса
      const headers = {
        'Content-Type': 'application/json'
      };
      console.log('[GoAuthApi] Request headers:', headers);
      console.log('[GoAuthApi] Request URL:', `${GO_API_URL}/auth/set-session-cookie`);
      console.log('[GoAuthApi] Request method: POST');
      console.log('[GoAuthApi] Request body:', { token: token.substring(0, 10) + '...' });
      
      const response = await axios.post(`${GO_API_URL}/auth/set-session-cookie`, { token }, {
        headers,
        withCredentials: true // Важно для работы с cookie
      });
      
      // Проверяем cookies после запроса
      const cookiesAfter = document.cookie.split(';')
        .map(cookie => cookie.trim())
        .filter(cookie => cookie.length > 0);
      console.log('[GoAuthApi] Cookies after request:', cookiesAfter);
      
      // Проверяем, добавились ли новые cookies
      const newCookies = cookiesAfter.filter(cookie => !cookiesBefore.includes(cookie));
      console.log('[GoAuthApi] New cookies after request:', newCookies);
      
      // Логируем ответ
      console.log('[GoAuthApi] Response status:', response.status);
      console.log('[GoAuthApi] Response data:', response.data);
      
      console.log('[GoAuthApi] Session setup completed successfully');
      return response.data;
    } catch (error) {
      console.error('[GoAuthApi] Session authentication setup failed');
      
      if (error.response) {
        console.error('[GoAuthApi] Error status:', error.response.status);
        console.error('[GoAuthApi] Error data:', error.response.data);
      } else if (error.request) {
        console.error('[GoAuthApi] No response received from server');
      } else {
        console.error('[GoAuthApi] Error message:', error.message);
      }
      
      throw error;
    }
  }
  */

  // Кеширование для проверки подписки
  static cachedSubscriptionStatus = null; // ИСПРАВЛЕНИЕ: null вместо false по умолчанию
  static subscriptionChecks = 0;
  static MAX_SUBSCRIPTION_CHECKS = 5;
  static lastSubscriptionCheck = 0;
  static SUBSCRIPTION_CACHE_TIME = 30000; // ИСПРАВЛЕНИЕ: Уменьшено до 30 секунд
  static subscriptionCheckInProgress = false;
  static subscriptionRequestQueue = [];
  static subscriptionRetryAttempted = false;

  // Метод для принудительного сброса кеша подписки
  static clearSubscriptionCache() {
    console.log('[GoAPI] 🧹 CLEARING subscription cache completely...');
    this.cachedSubscriptionStatus = null; // ИСПРАВЛЕНИЕ: null вместо false
    this.lastSubscriptionCheck = 0;
    this.subscriptionChecks = 0;
    this.subscriptionCheckInProgress = false;
    this.subscriptionRetryAttempted = false;
    window.__SUBSCRIPTION_CHECK_IN_PROGRESS = false;
    console.log('[GoAPI] ✅ Subscription cache cleared - forcing fresh check');
  }

  // Улучшенная проверка подписки с кешированием и защитой от циклов
  static async checkSubscription(token) {
    // Увеличиваем счетчик
    this.subscriptionChecks++;
    
    try {
      // ИСПРАВЛЕНИЕ: Более строгая проверка глобального флага
      if (window.__SUBSCRIPTION_CHECK_IN_PROGRESS) {
        console.log(`[GoAPI] ⏳ Global subscription check in progress (check #${this.subscriptionChecks})`);
        // ИСПРАВЛЕНИЕ: НЕ возвращаем кеш, если он может быть устаревшим
        if (this.cachedSubscriptionStatus && (Date.now() - this.lastSubscriptionCheck < 5000)) {
          console.log(`[GoAPI] Using very recent cached status:`, this.cachedSubscriptionStatus);
          return this.cachedSubscriptionStatus;
        }
        console.log(`[GoAPI] ❌ Cache too old or empty, returning default inactive status`);
        return { isActive: false, isTrialing: false };
      }

      // ИСПРАВЛЕНИЕ: Более строгая проверка прогресса
      if (this.subscriptionCheckInProgress) {
        console.log(`[GoAPI] ⏳ Subscription check already in progress (check #${this.subscriptionChecks})`);
        // ИСПРАВЛЕНИЕ: Только возвращаем кеш если он очень свежий
        if (this.cachedSubscriptionStatus && (Date.now() - this.lastSubscriptionCheck < 5000)) {
          console.log(`[GoAPI] Using very recent cached status during check:`, this.cachedSubscriptionStatus);
          return this.cachedSubscriptionStatus;
        }
        console.log(`[GoAPI] ❌ No recent cache available, returning default inactive status`);
        return { isActive: false, isTrialing: false };
      }

      // ИСПРАВЛЕНИЕ: Более строгая проверка кеша
      const now = Date.now();
      const cacheAge = now - this.lastSubscriptionCheck;
      const isCacheValid = this.cachedSubscriptionStatus &&
                          this.lastSubscriptionCheck > 0 &&
                          cacheAge < this.SUBSCRIPTION_CACHE_TIME;

      if (isCacheValid) {
        console.log(`[GoAPI] ✅ Using valid cached subscription status (check #${this.subscriptionChecks})`);
        console.log(`[GoAPI] 📊 Cached status:`, this.cachedSubscriptionStatus);
        console.log(`[GoAPI] ⏰ Cache age: ${cacheAge}ms (limit: ${this.SUBSCRIPTION_CACHE_TIME}ms)`);
        return this.cachedSubscriptionStatus;
      } else if (this.cachedSubscriptionStatus) {
        console.log(`[GoAPI] ⚠️ Cache exists but expired (age: ${cacheAge}ms), will refresh`);
      } else {
        console.log(`[GoAPI] 🆕 No cache available, making fresh request`);
      }
      
      // Ограничение на количество запросов
      if (this.subscriptionChecks > this.MAX_SUBSCRIPTION_CHECKS && this.cachedSubscriptionStatus) {
        console.log(`[GoAPI] Max subscription checks (${this.subscriptionChecks}) exceeded, using cached result:`, this.cachedSubscriptionStatus);
        return this.cachedSubscriptionStatus;
      }
      
      // Устанавливаем флаг выполнения
      this.subscriptionCheckInProgress = true;
      window.__SUBSCRIPTION_CHECK_IN_PROGRESS = true;
      
      console.log(`[GoAPI] Making subscription check request (check #${this.subscriptionChecks})`);
      console.log(`[GoAPI] Token length: ${token ? token.length : 'no token'}`);
      console.log(`[GoAPI] Request URL: ${GO_API_URL}/subscription`);
      
      // ИСПРАВЛЕНИЕ: Обеспечиваем корректную передачу токена
      const headers = {};
      if (token) {
        // Убедимся, что токен передается в правильном формате
        headers['Authorization'] = `Bearer ${token}`;
        headers['Content-Type'] = 'application/json';
        console.log(`[GoAPI] Request headers prepared with Authorization Bearer token`);
        console.log(`[GoAPI] Token prefix: ${token.substring(0, 20)}...`);
      } else {
        console.warn(`[GoAPI] No token provided for subscription check`);
      }
      
      // Добавляем дополнительные заголовки для совместимости
      headers['X-Requested-With'] = 'XMLHttpRequest';
      headers['Accept'] = 'application/json';
      
      // Добавляем таймаут для запроса
      const response = await axios.get(`${GO_API_URL}/subscription`, {
        headers,
        withCredentials: true, // Важно для чтения cookie
        timeout: 15000, // 15 секунд таймаут
        // Добавляем дополнительные опции для отладки
        validateStatus: function (status) {
          // Принимаем статусы 200-299 и 401 для дальнейшей обработки
          return (status >= 200 && status < 300) || status === 401;
        }
      });
      
      // Специальная обработка 401 ошибки
      if (response.status === 401) {
        console.error(`[GoAPI] 401 Unauthorized - token may be invalid or expired`);
        console.error(`[GoAPI] Response data:`, response.data);
        
        // Пытаемся обновить токен и повторить запрос
        try {
          console.log(`[GoAPI] Attempting to refresh token and retry subscription check`);
          const freshToken = await this.refreshToken();
          
          if (freshToken && freshToken !== token) {
            console.log(`[GoAPI] Token refreshed, retrying subscription check`);
            
            // Повторяем запрос с новым токеном
            const retryResponse = await axios.get(`${GO_API_URL}/subscription`, {
              headers: {
                'Authorization': `Bearer ${freshToken}`,
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
              },
              withCredentials: true,
              timeout: 15000,
              validateStatus: function (status) {
                return (status >= 200 && status < 300);
              }
            });
            
            // Обновляем кеш и возвращаем результат
            this.cachedSubscriptionStatus = retryResponse.data || { isActive: false, isTrialing: false };
            this.lastSubscriptionCheck = Date.now();
            
            console.log(`[GoAPI] Subscription check successful after token refresh:`, retryResponse.data);
            return retryResponse.data;
          }
        } catch (refreshError) {
          console.error(`[GoAPI] Token refresh failed:`, refreshError);
        }
        
        // ИСПРАВЛЕНИЕ: Если обновление токена не удалось, возвращаем безопасный результат
        console.log(`[GoAPI] ❌ Token refresh failed, returning safe inactive status`);
        return { isActive: false, isTrialing: false };
      }
      
      // Обновляем кеш и метку времени
      this.cachedSubscriptionStatus = response.data || { isActive: false, isTrialing: false };
      this.lastSubscriptionCheck = Date.now();
      
      console.log(`[GoAPI] Subscription check successful (check #${this.subscriptionChecks})`);
      console.log(`[GoAPI] Response status: ${response.status}`);
      console.log(`[GoAPI] Response data:`, response.data);
      console.log(`[GoAPI] Parsed isActive: ${response.data?.isActive}`);
      console.log(`[GoAPI] Parsed isTrialing: ${response.data?.isTrialing}`);
      console.log(`[GoAPI] Cached status:`, this.cachedSubscriptionStatus);
      
      // Уведомляем всех ожидающих
      while (this.subscriptionRequestQueue.length > 0) {
        const resolve = this.subscriptionRequestQueue.shift();
        resolve(response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error(`[GoAPI] Subscription check error (check #${this.subscriptionChecks}):`, error);
      
      // Более детальная обработка ошибок
      if (error.response) {
        console.error(`[GoAPI] Error response status: ${error.response.status}`);
        console.error(`[GoAPI] Error response data:`, error.response.data);
        console.error(`[GoAPI] Error response headers:`, error.response.headers);
        
        // Специальная обработка 401 ошибки
        if (error.response.status === 401) {
          console.error(`[GoAPI] 401 Unauthorized - checking token validity`);
          if (token) {
            console.error(`[GoAPI] Token was provided but rejected by server`);
            console.error(`[GoAPI] Token length: ${token.length}`);
            console.error(`[GoAPI] Token prefix: ${token.substring(0, 20)}...`);
            
            // Пытаемся обновить токен и повторить запрос только один раз
            if (!this.subscriptionRetryAttempted) {
              this.subscriptionRetryAttempted = true;
              
              try {
                console.log(`[GoAPI] Attempting to refresh token due to 401 error`);
                const freshToken = await this.refreshToken();
                
                if (freshToken && freshToken !== token) {
                  console.log(`[GoAPI] Token refreshed, retrying subscription check after 401`);
                  
                  // Сбрасываем флаги для повторной попытки
                  this.subscriptionCheckInProgress = false;
                  window.__SUBSCRIPTION_CHECK_IN_PROGRESS = false;
                  
                  // Рекурсивно вызываем checkSubscription с новым токеном
                  return await this.checkSubscription(freshToken);
                }
              } catch (refreshError) {
                console.error(`[GoAPI] Token refresh after 401 failed:`, refreshError);
              } finally {
                // Сбрасываем флаг попытки через некоторое время
                setTimeout(() => {
                  this.subscriptionRetryAttempted = false;
                }, 60000); // 1 минута
              }
            }
          }
        }
      } else if (error.request) {
        console.error(`[GoAPI] No response received:`, error.request);
      } else {
        console.error(`[GoAPI] Error setting up request:`, error.message);
      }
      
      // Если была ошибка сети, устанавливаем флаг в window
      if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
        window.__SUBSCRIPTION_CHECK_FAILED_RECENTLY = true;
        window.__SUBSCRIPTION_CHECK_FAILED_TIME = Date.now().toString();
      }
      
      // ИСПРАВЛЕНИЕ: Уведомляем всех ожидающих об ошибке с безопасным результатом
      while (this.subscriptionRequestQueue.length > 0) {
        const resolve = this.subscriptionRequestQueue.shift();
        resolve({ isActive: false, isTrialing: false });
      }

      // ИСПРАВЛЕНИЕ: В случае ошибки ВСЕГДА возвращаем безопасный результат
      console.log(`[GoAPI] ❌ Error occurred, returning safe inactive status (not using potentially stale cache)`);
      return { isActive: false, isTrialing: false };
    } finally {
      // Сбрасываем флаги
      this.subscriptionCheckInProgress = false;
      window.__SUBSCRIPTION_CHECK_IN_PROGRESS = false;
    }
  }

  // Кеширование для профиля пользователя
  static cachedUserProfile = null;
  static profileChecks = 0;
  static MAX_PROFILE_CHECKS = 5;
  static lastProfileCheck = 0;
  static PROFILE_CACHE_TIME = 300000; // 5 минут
  static profileCheckInProgress = false;
  static profileRetries = 0;
  static MAX_PROFILE_RETRIES = 3;
  static profileRequestQueue = [];
  
  // Извлечение информации из JWT токена
  static extractUserInfoFromJWT(token) {
    if (!token) return null;
    
    try {
      // Извлекаем payload из JWT
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => 
        '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      ).join(''));
      
      const payload = JSON.parse(jsonPayload);
      
      // Создаем минимальный профиль с данными из токена
      if (payload && payload.sub) {
        return {
          id: payload.sub,
          email: payload.email || 'unknown',
          username: payload.preferred_username || 'User',
          firstName: payload.given_name || 'Guest',
          lastName: payload.family_name || 'User',
          name: payload.name || 'Guest User',
          isFromJWT: true,
          jwtIssuer: payload.iss,
          expiration: payload.exp,
          created_at: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('[GoAPI] Error extracting user info from JWT:', error);
    }
    
    return null;
  }

  // Получение профиля пользователя с поддержкой cookie и кешированием
  static async getUserProfile(token) {
    // Увеличиваем счетчик
    this.profileChecks++;
    
    try {
      // Проверяем кеш
      const now = Date.now();
      if (this.cachedUserProfile && (now - this.lastProfileCheck < this.PROFILE_CACHE_TIME)) {
        console.log(`[GoAPI] Using cached user profile (check #${this.profileChecks})`);
        return this.cachedUserProfile;
      }
      
      // Пытаемся получить профиль из JWT как запасной вариант перед проверкой флага
      const jwtProfile = this.extractUserInfoFromJWT(token);
      
      // Если уже идет проверка, возвращаем последний известный результат
      if (this.profileCheckInProgress) {
        console.log(`[GoAPI] Profile check already in progress (check #${this.profileChecks}), using cached profile`);
        
        // Приоритетно возвращаем кешированный профиль, если есть
        if (this.cachedUserProfile) {
          return this.cachedUserProfile;
        }
        
        // Если у нас есть профиль из JWT, возвращаем его как промежуточный вариант
        if (jwtProfile) {
          console.log(`[GoAPI] Using profile from JWT as fallback while waiting for profile check`);
          this.cachedUserProfile = jwtProfile; // Сохраняем в кеш для возможного использования в будущем
          this.lastProfileCheck = Date.now() - this.PROFILE_CACHE_TIME/2; // Установим меньшее время кеша для JWT профиля
          return jwtProfile;
        }
        
        // Ждем немного, чтобы дать шанс завершиться текущей проверке
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // Если после ожидания уже есть кешированное значение, возвращаем его
        if (this.cachedUserProfile) {
          return this.cachedUserProfile;
        }
      }
      
      // Ограничение на количество запросов
      if (this.profileChecks > this.MAX_PROFILE_CHECKS) {
        if (this.cachedUserProfile) {
          console.log(`[GoAPI] Max profile checks (${this.profileChecks}) exceeded, using cached profile`);
          return this.cachedUserProfile;
        } else if (jwtProfile) {
          console.log(`[GoAPI] Max profile checks (${this.profileChecks}) exceeded, using JWT profile`);
          this.cachedUserProfile = jwtProfile;
          this.lastProfileCheck = Date.now();
          return jwtProfile;
        }
      }
      
      // Устанавливаем флаг выполнения
      this.profileCheckInProgress = true;
      
      // Добавляем детальную диагностику
      console.log(`[GoAPI] Making user profile request (check #${this.profileChecks})`);
      console.log(`[GoAPI] Token length: ${token ? token.length : 'no token'}`);
      console.log(`[GoAPI] Token prefix: ${token ? token.substring(0, 10) + '...' : 'no token'}`);
      
      const headers = {};
      // Добавляем токен в заголовки, только если он указан явно
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      try {
        // Добавляем таймаут для запроса
        const response = await Promise.race([
          axios.get(`${GO_API_URL}/user/profile`, {
            headers: {
              ...headers,
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest',
              'Accept': 'application/json'
            },
            withCredentials: true, // Важно для чтения cookie
            timeout: 15000, // 15 секунд таймаут
            // Добавляем обработку статусов
            validateStatus: function (status) {
              return (status >= 200 && status < 300) || status === 401;
            }
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('User profile request timeout')), 15000)
          )
        ]);
        
        // Специальная обработка 401 ошибки
        if (response.status === 401) {
          console.error(`[GoAPI] Profile 401 Unauthorized - token may be invalid`);
          console.error(`[GoAPI] Profile response data:`, response.data);
          
          // Если у нас есть JWT профиль, используем его вместо ошибки
          if (jwtProfile) {
            console.log(`[GoAPI] Using JWT profile as fallback after 401 response`);
            this.cachedUserProfile = jwtProfile;
            this.lastProfileCheck = Date.now();
            return jwtProfile;
          }
          
          throw new Error('Profile request returned 401 Unauthorized');
        }
        
        if (!response || !response.data || !response.data.id) {
          console.error(`[GoAPI] User profile response invalid:`, response.data);
          
          // Если у нас есть JWT профиль, используем его вместо ошибки
          if (jwtProfile) {
            console.log(`[GoAPI] Using JWT profile as fallback after invalid response`);
            this.cachedUserProfile = jwtProfile;
            this.lastProfileCheck = Date.now();
            return jwtProfile;
          }
          
          throw new Error('Invalid user profile data received');
        }
        
        // Обновляем кеш и метку времени
        this.cachedUserProfile = response.data;
        this.lastProfileCheck = Date.now();
        this.profileRetries = 0; // Сбрасываем счетчик повторных попыток при успехе
        
        console.log(`[GoAPI] User profile request successful (check #${this.profileChecks}), user ID: ${response.data.id}`);
        
        return response.data;
      } catch (error) {
        console.error(`[GoAPI] Error fetching user profile (check #${this.profileChecks}):`, error.message || error);
        
        // Увеличиваем счетчик повторных попыток
        this.profileRetries++;
        
        // Если у нас есть профиль из JWT, используем его при ошибке
        if (jwtProfile) {
          console.log(`[GoAPI] Using JWT profile after fetch error`);
          this.cachedUserProfile = jwtProfile;
          this.lastProfileCheck = Date.now();
          return jwtProfile;
        }
        
        // Если есть кешированное значение и превышен лимит повторных попыток,
        // возвращаем кешированное значение
        if (this.cachedUserProfile && this.profileRetries > this.MAX_PROFILE_RETRIES) {
          console.log(`[GoAPI] Using cached profile after ${this.profileRetries} failed attempts`);
          return this.cachedUserProfile;
        }
        
        // Искусственная задержка перед повторной попыткой по экспоненциальному backoff
        const delay = Math.min(1000 * Math.pow(2, this.profileRetries), 10000);
        
        // Если слишком много ошибок, предотвращаем циклические запросы
        if (this.profileRetries <= this.MAX_PROFILE_RETRIES) {
          console.log(`[GoAPI] Will retry profile request after ${delay}ms delay (attempt ${this.profileRetries}/${this.MAX_PROFILE_RETRIES})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Повторяем попытку
          this.profileCheckInProgress = false;
          return this.getUserProfile(token);
        }
        
        // Если у нас есть кешированный профиль, возвращаем его как запасной вариант
        if (this.cachedUserProfile) {
          return this.cachedUserProfile;
        }
        
        // В крайнем случае, создаем минимальный объект профиля
        const fallbackProfile = {
          id: token ? 'auth_user' : 'unknown',
          email: 'unknown',
          username: 'Guest User',
          firstName: 'Guest',
          lastName: 'User',
          name: 'Guest User',
          isFallback: true,
          created_at: new Date().toISOString()
        };
        
        this.cachedUserProfile = fallbackProfile;
        this.lastProfileCheck = Date.now();
        return fallbackProfile;
      }
    } catch (error) {
      console.error(`[GoAPI] Critical error in getUserProfile (check #${this.profileChecks}):`, error.message || error);
      
      // В случае критической ошибки также пробуем JWT профиль
      const jwtProfile = this.extractUserInfoFromJWT(token);
      if (jwtProfile) {
        console.log(`[GoAPI] Using JWT profile after critical error`);
        this.cachedUserProfile = jwtProfile;
        this.lastProfileCheck = Date.now();
        return jwtProfile;
      }
      
      // В случае ошибки возвращаем последний известный результат
      if (this.cachedUserProfile) {
        return this.cachedUserProfile;
      }
      
      // В крайнем случае, создаем минимальный объект профиля
      const fallbackProfile = {
        id: token ? 'auth_user' : 'unknown',
        email: 'unknown',
        username: 'Guest User',
        firstName: 'Guest',
        lastName: 'User',
        name: 'Guest User',
        isFallback: true,
        created_at: new Date().toISOString()
      };
      
      this.cachedUserProfile = fallbackProfile;
      this.lastProfileCheck = Date.now();
      return fallbackProfile;
    } finally {
      // Сбрасываем флаг с небольшой задержкой
      setTimeout(() => {
        this.profileCheckInProgress = false;
      }, 500);
    }
  }

  // Создание платежной сессии Stripe с поддержкой cookie
  static async createCheckout(token, priceId) {
    try {
      const headers = {
        'Content-Type': 'application/json'
      };
      // Добавляем токен в заголовки, только если он указан явно
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await axios.post(`${GO_API_URL}/checkout`, { priceId }, {
        headers,
        withCredentials: true // Важно для чтения cookie
      });
      return response.data;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }
}

export default GoAuthApi;