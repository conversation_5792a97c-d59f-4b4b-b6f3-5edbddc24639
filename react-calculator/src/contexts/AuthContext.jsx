import React, { createContext, useContext, useState, useEffect } from 'react';
import { GoAuthApi } from '../api/goApi';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(false);

  // Простая проверка авторизации
  const checkAuthState = async () => {
    try {
      console.log('[SimpleAuth] 🔄 Checking authentication state...');
      console.log('[SimpleAuth] 📊 Current Clerk state:', {
        user: window.Clerk?.user?.id || 'null',
        loaded: window.Clerk?.loaded || false,
        session: window.Clerk?.session?.id || 'null'
      });

      // ИСПРАВЛЕНИЕ: Принудительно очищаем кеш подписки при каждой проверке
      console.log('[SimpleAuth] 🧹 Force clearing subscription cache before check...');
      GoAuthApi.clearSubscriptionCache();
      
      // Проверяем есть ли авторизованный пользователь в Clerk
      if (window.Clerk?.user) {
        console.log('[SimpleAuth] ✅ Found authenticated user:', window.Clerk.user.id);
        console.log('[SimpleAuth] 👤 User email:', window.Clerk.user.emailAddresses?.[0]?.emailAddress || 'unknown');
        
        try {
          // Получаем токен и профиль пользователя
          console.log('[SimpleAuth] 🎫 Requesting session token...');
          const token = await window.Clerk.session?.getToken();
          if (token) {
            console.log('[SimpleAuth] ✅ Token obtained successfully (length:', token.length, ')');
            console.log('[SimpleAuth] 📝 Loading user profile from GoAuthApi...');
            
            const profile = await GoAuthApi.getUserProfile(token);
            
            console.log('[SimpleAuth] ✅ User profile loaded:', profile?.id);
            
            // ИСПРАВЛЕНИЕ: Очищаем кеш подписки для свежей проверки
            console.log('[SimpleAuth] 🗑️ Clearing subscription cache for fresh check...');
            GoAuthApi.clearSubscriptionCache();
            
            // ИСПРАВЛЕНИЕ: Проверяем подписку через отдельный API вызов
            console.log('[SimpleAuth] 🔍 Checking subscription status...');
            const subscriptionStatus = await GoAuthApi.checkSubscription(token);
            console.log('[SimpleAuth] 📊 Subscription check result:', subscriptionStatus);

            // ИСПРАВЛЕНИЕ: Дополнительное логирование для отладки
            const isActiveValue = subscriptionStatus?.isActive === true;
            console.log('[SimpleAuth] 🔍 DETAILED SUBSCRIPTION ANALYSIS:');
            console.log('[SimpleAuth] - Raw subscriptionStatus:', subscriptionStatus);
            console.log('[SimpleAuth] - subscriptionStatus.isActive:', subscriptionStatus?.isActive);
            console.log('[SimpleAuth] - Type of isActive:', typeof subscriptionStatus?.isActive);
            console.log('[SimpleAuth] - Final isSubscribed value:', isActiveValue);

            setIsAuthenticated(true);
            setUserProfile(profile);
            setIsSubscribed(isActiveValue);
            
            console.log('[SimpleAuth] ✅ User authenticated and profile loaded');
            console.log('[SimpleAuth] 📈 Final auth state:', {
              authenticated: true,
              userId: profile?.id,
              subscribed: subscriptionStatus?.isActive === true
            });
          } else {
            console.log('[SimpleAuth] ❌ No token available from Clerk session');
            console.log('[SimpleAuth] 🔍 Session details:', {
              session: window.Clerk.session,
              sessionId: window.Clerk.session?.id
            });
            setIsAuthenticated(false);
          }
        } catch (error) {
          console.error('[SimpleAuth] 💥 Error loading profile or subscription:', error);
          console.error('[SimpleAuth] Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
          setIsAuthenticated(false);
        }
      } else {
        console.log('[SimpleAuth] ❌ No authenticated user found in Clerk');
        console.log('[SimpleAuth] 🔍 Clerk object details:', {
          clerkExists: !!window.Clerk,
          loaded: window.Clerk?.loaded,
          user: window.Clerk?.user,
          session: window.Clerk?.session
        });
        setIsAuthenticated(false);
        setUserProfile(null);
        setIsSubscribed(false);
      }
    } catch (error) {
      console.error('[SimpleAuth] 💥 Critical error checking auth state:', error);
      console.error('[SimpleAuth] Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
      console.log('[SimpleAuth] 🏁 Auth check completed, loading set to false');
    }
  };

  // Функция выхода
  const signOut = async () => {
    try {
      console.log('[SimpleAuth] Signing out...');
      
      if (window.Clerk?.signOut) {
        await window.Clerk.signOut();
      }
      
      // Очищаем локальные данные
      if (window.electronAPI?.clearAllDataIncludingWebView) {
        await window.electronAPI.clearAllDataIncludingWebView();
      }
      
      setIsAuthenticated(false);
      setUserProfile(null);
      setIsSubscribed(false);
      
      console.log('[SimpleAuth] ✅ Successfully signed out');
    } catch (error) {
      console.error('[SimpleAuth] Error during sign out:', error);
    }
  };
  // 🚨 РЕШЕНИЕ OAuth ПРОБЛЕМЫ: Более агрессивная проверка состояния
  useEffect(() => {
    let intervalId;
    let oauthIntervalId; // Специальный интервал для OAuth
    
    const startChecking = () => {
      console.log('[SimpleAuth] 🚀 Starting enhanced auth monitoring for OAuth...');
      
      // 🔧 ИНИЦИАЛИЗАЦИЯ: Создаем переменные для проверки URL
      const currentUrl = window.location.href;
      const urlParams = new URLSearchParams(window.location.search);
      const isElectron = window.electronAPI !== undefined;
      const hasHandshake = urlParams.has('__clerk_handshake');
      const handshakeValue = urlParams.get('__clerk_handshake');
      
      // 🚨 ЭКСТРЕННАЯ ОЧИСТКА: Если в Electron есть handshake - немедленно убираем из URL
      if (isElectron && hasHandshake) {
        console.log('[SimpleAuth] 🧹 EMERGENCY CLEANUP: Removing handshake from Electron URL to stop loops');
        const newUrl = new URL(window.location);
        newUrl.searchParams.delete('__clerk_handshake');
        window.history.replaceState({}, document.title, newUrl.toString());
        console.log('[SimpleAuth] ✅ URL cleaned, handshake removed');
        return; // Выходим, не обрабатываем handshake в Electron
      }
      
      // 🎯 ДЕТЕКЦИЯ OAuth COMPLETION: Проверяем URL параметры
      const hasClerkHandshake = hasHandshake; // Используем уже вычисленное значение
      
      // 🔧 ОТЛАДКА ДЕТЕКЦИИ
      console.log('[SimpleAuth] 🔍 OAuth Detection Debug:', {
        currentUrl,
        searchParams: window.location.search,
        hasHandshake: hasClerkHandshake,
        handshakeLength: handshakeValue ? handshakeValue.length : 0,
        allParams: Object.fromEntries(urlParams.entries())
      });
      
      if (hasClerkHandshake) {
        console.log('[SimpleAuth] 🎉 DETECTED OAuth completion via URL parameters!');
        console.log('[SimpleAuth] 🔗 Full URL:', currentUrl);
        console.log('[SimpleAuth] 🎯 Handshake parameter detected, OAuth completed!');
        
        // 🚨 BACKUP МЕХАНИЗМ: ТОЛЬКО если Universal Handler не работает
        const isElectron = window.electronAPI !== undefined;
        if (isElectron) {
          console.log('[SimpleAuth] 🔄 In Electron - Universal Handler should have handled this');
          console.log('[SimpleAuth] 🧹 Cleaning handshake from URL to prevent loops...');
          
          // Очищаем URL от handshake в Electron чтобы избежать бесконечных циклов
          const newUrl = new URL(window.location);
          newUrl.searchParams.delete('__clerk_handshake');
          window.history.replaceState({}, document.title, newUrl.toString());
          
          // В Electron НЕ вызываем дополнительные protocol calls
        } else {
          console.log('[SimpleAuth] 🔄 In system browser - this is backup case');
          
          try {
            // Вызываем querapp:// protocol как backup (только в браузере)
            const protocolUrl = `querapp://auth-callback?__clerk_handshake=${encodeURIComponent(handshakeValue)}`;
            console.log('[SimpleAuth] 🔄 Calling backup protocol:', protocolUrl.substring(0, 100) + '...');
            
            // Метод 1: window.location
            window.location.href = protocolUrl;
            
          } catch (error) {
            console.error('[SimpleAuth] ❌ Error calling backup protocol:', error);
          }
        }
        
        console.log('[SimpleAuth] 🔧 Starting IMMEDIATE auth state refresh...');
        
        // МГНОВЕННАЯ проверка состояния после OAuth (несколько попыток)
        setTimeout(() => {
          console.log('[SimpleAuth] 🔄 OAuth check attempt #1');
          checkAuthState();
        }, 100);
        setTimeout(() => {
          console.log('[SimpleAuth] 🔄 OAuth check attempt #2');
          checkAuthState();
        }, 500);
        setTimeout(() => {
          console.log('[SimpleAuth] 🔄 OAuth check attempt #3');
          checkAuthState();
        }, 1000);
        setTimeout(() => {
          console.log('[SimpleAuth] 🔄 OAuth check attempt #4 + FORCE SESSION RELOAD');
          // Принудительно перезагружаем Clerk session
          if (window.Clerk?.session) {
            window.Clerk.session.reload?.();
          }
          checkAuthState();
        }, 2000);
      }
      
      checkAuthState();
      
      // Обычная проверка каждые 2 секунды + ПОСТОЯННАЯ ПРОВЕРКА URL
      intervalId = setInterval(() => {
        // 🔍 ПОСТОЯННАЯ ПРОВЕРКА URL на handshake параметр
        const urlParams = new URLSearchParams(window.location.search);
        const hasClerkHandshake = urlParams.has('__clerk_handshake');
        
        if (hasClerkHandshake && !isAuthenticated) {
          console.log('[SimpleAuth] 🎉 HANDSHAKE DETECTED in polling! Triggering OAuth completion...');
          checkAuthState(); // Немедленная проверка
        }
        
        const currentClerkState = {
          loaded: window.Clerk?.loaded || false,
          hasUser: Boolean(window.Clerk?.user),
          userId: window.Clerk?.user?.id || null,
          sessionId: window.Clerk?.session?.id || null
        };
        
        // Проверяем изменения в состоянии
        const stateChanged = 
          currentClerkState.hasUser !== isAuthenticated ||
          (currentClerkState.hasUser && !isAuthenticated) ||
          (!currentClerkState.hasUser && isAuthenticated);
        
        if (stateChanged) {
          console.log('[SimpleAuth] 🔄 Clerk state changed, rechecking...', {
            previous: { isAuthenticated },
            current: currentClerkState
          });
          checkAuthState();
        }
      }, 2000);
      
      // 🚨 СПЕЦИАЛЬНАЯ ПРОВЕРКА ДЛЯ OAuth: каждые 500ms в течение 30 секунд
      // Это поможет быстро подхватить сессию после OAuth без redirect
      let oauthCheckCount = 0;
      const maxOAuthChecks = 60; // 30 секунд при проверке каждые 500ms
      
      oauthIntervalId = setInterval(() => {
        oauthCheckCount++;
        
        // Проверяем есть ли новая сессия после OAuth
        if (window.Clerk?.user && !isAuthenticated) {
          console.log('[SimpleAuth] 🎉 OAuth SUCCESS detected! User found, updating state...');
          checkAuthState();
          clearInterval(oauthIntervalId); // Прекращаем агрессивную проверку
        }
        
        // Прекращаем агрессивную проверку через 30 секунд
        if (oauthCheckCount >= maxOAuthChecks) {
          console.log('[SimpleAuth] ⏰ OAuth monitoring period ended');
          clearInterval(oauthIntervalId);
        }
      }, 500);
      
      console.log('[SimpleAuth] ✅ Enhanced auth monitoring started (normal + OAuth)');
    };

    // Ждем инициализации Clerk
    if (window.Clerk?.loaded) {
      console.log('[SimpleAuth] 📋 Clerk already loaded, starting immediately');
      startChecking();
    } else {
      console.log('[SimpleAuth] ⏳ Waiting for Clerk to load...');
      
      // Ждем загрузки Clerk
      const checkClerkReady = setInterval(() => {
        if (window.Clerk?.loaded) {
          console.log('[SimpleAuth] ✅ Clerk loaded, starting auth monitoring');
          clearInterval(checkClerkReady);
          startChecking();
        } else {
          console.log('[SimpleAuth] ⏳ Still waiting for Clerk...');
        }
      }, 100);
      
      // Очищаем через 10 секунд если Clerk не загрузился
      setTimeout(() => {
        clearInterval(checkClerkReady);
        if (!window.Clerk?.loaded) {
          console.warn('[SimpleAuth] ⚠️ Clerk not loaded after 10s, proceeding anyway');
          setIsLoading(false);
        }
      }, 10000);
    }

    return () => {
      console.log('[SimpleAuth] 🧹 Cleaning up enhanced auth monitoring...');
      if (intervalId) {
        clearInterval(intervalId);
      }
      if (oauthIntervalId) {
        clearInterval(oauthIntervalId);
      }
    };
  }, [isAuthenticated]);

  // Слушатель focus окна для обновления статуса подписки
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated) {
        console.log('[SimpleAuth] Window focused, refreshing subscription status...');
        checkAuthState();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [isAuthenticated]);

  // Дополнительные функции для совместимости со старым AuthContext
  const openSubscriptionPage = () => {
    console.log('[SimpleAuth] Opening subscription page...');
    // Динамически определяем базовый URL на основе текущего домена
    const baseUrl = window.location.origin;
    const pricingUrl = `${baseUrl}/terminal/pricing`;
    
    // В Electron можем открыть внешнюю ссылку
    if (window.electronAPI?.openExternal) {
      window.electronAPI.openExternal(pricingUrl);
    } else {
      window.open(pricingUrl, '_blank');
    }
  };

  const forceCheckSubscription = async () => {
    console.log('[SimpleAuth] Force checking subscription...');
    setIsCheckingSubscription(true);
    try {
      // Очищаем кеш для принудительной проверки
      GoAuthApi.clearSubscriptionCache();
      
      // Получаем токен
      const token = await window.Clerk.session?.getToken();
      if (token) {
        console.log('[SimpleAuth] 🔍 Force checking subscription with fresh token...');
        const subscriptionStatus = await GoAuthApi.checkSubscription(token);
        console.log('[SimpleAuth] 📊 Force subscription check result:', subscriptionStatus);
        
        setIsSubscribed(subscriptionStatus?.isActive === true);
        console.log('[SimpleAuth] Subscription status updated:', subscriptionStatus?.isActive ? 'Active' : 'Inactive');
      } else {
        console.log('[SimpleAuth] ❌ No token available for force subscription check');
        setIsSubscribed(false);
      }
    } catch (error) {
      console.error('[SimpleAuth] Error during force subscription check:', error);
    } finally {
      setIsCheckingSubscription(false);
    }
  };

  const handleClerkSignIn = async (token) => {
    console.log('[SimpleAuth] Handling Clerk sign in with token...');
    try {
      if (token) {
        console.log('[SimpleAuth] 📋 Loading user profile...');
        const profile = await GoAuthApi.getUserProfile(token);
        console.log('[SimpleAuth] ✅ Profile loaded:', profile?.id);
        
        // Очищаем кеш подписки для нового пользователя
        console.log('[SimpleAuth] 🗑️ Clearing subscription cache for new sign-in...');
        GoAuthApi.clearSubscriptionCache();
        
        console.log('[SimpleAuth] 🔍 Checking subscription for signed in user...');
        const subscriptionStatus = await GoAuthApi.checkSubscription(token);
        console.log('[SimpleAuth] 📊 Sign-in subscription check result:', subscriptionStatus);
        
        setIsAuthenticated(true);
        setUserProfile(profile);
        setIsSubscribed(subscriptionStatus?.isActive === true);
        
        console.log('[SimpleAuth] ✅ Sign in successful');
        console.log('[SimpleAuth] Final subscription status:', subscriptionStatus?.isActive ? 'Active' : 'Inactive');
        return subscriptionStatus?.isActive === true;
      }
      return false;
    } catch (error) {
      console.error('[SimpleAuth] Error in handleClerkSignIn:', error);
      return false;
    }
  };

  const value = {
    isAuthenticated,
    isLoading,
    isSubscribed,
    isCheckingSubscription,
    userProfile,
    signOut,
    refreshAuth: checkAuthState,
    openSubscriptionPage,
    forceCheckSubscription,
    handleClerkSignIn
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;