// Тестовый скрипт для проверки исправления кеширования
// Запустить в консоли браузера для проверки

console.log('🧪 TESTING CACHE FIX...');

// Проверяем текущее состояние кеша
console.log('📊 Current cache state:');
console.log('- cachedSubscriptionStatus:', window.GoAuthApi?.cachedSubscriptionStatus);
console.log('- lastSubscriptionCheck:', window.GoAuthApi?.lastSubscriptionCheck);
console.log('- subscriptionChecks:', window.GoAuthApi?.subscriptionChecks);

// Очищаем кеш
if (window.GoAuthApi) {
  console.log('🧹 Clearing cache...');
  window.GoAuthApi.clearSubscriptionCache();
  
  console.log('📊 Cache after clearing:');
  console.log('- cachedSubscriptionStatus:', window.GoAuthApi.cachedSubscriptionStatus);
  console.log('- lastSubscriptionCheck:', window.GoAuthApi.lastSubscriptionCheck);
  console.log('- subscriptionChecks:', window.GoAuthApi.subscriptionChecks);
} else {
  console.log('❌ GoAuthApi not available');
}

console.log('✅ Test completed. Check the logs above.');
